# ExcelUploader 组件

ExcelUploader 是一个功能强大的 Excel 文件上传和处理组件，支持两种工作模式：

1. **本地解析模式**：在前端直接解析 Excel 文件内容，进行数据验证和处理
2. **S3 上传模式**：将 Excel 文件上传到 S3 存储，由服务端处理文件内容

## 功能特性

- ✅ 支持 `.xlsx` 和 `.xls` 格式
- ✅ 支持三种上传交互方式：按钮、图标、拖拽
- ✅ 本地 Excel 解析和数据验证
- ✅ S3 文件上传（复用 FileUpload 组件）
- ✅ 动态配置系统，灵活组合上传模式
- ✅ 自定义列配置和验证规则
- ✅ 错误数据导出功能
- ✅ 进度显示和状态反馈
- ✅ 可配置的 UI 样式和文案
- ✅ 样式与逻辑解耦，组件间可复用

## 基本用法

### 本地解析模式

```tsx
import ExcelUploader from "./ExcelUploader";

const columnConfig = [
  {
    key: "name",
    title: "姓名",
    required: true,
    type: "string",
  },
  {
    key: "email",
    title: "邮箱",
    required: true,
    type: "string",
    validator: "email",
  },
];

<ExcelUploader
  visible={visible}
  onCancel={() => setVisible(false)}
  downLoadTemplate={handleDownloadTemplate}
  columnConfig={columnConfig}
  onSuccess={(data) => console.log("解析成功:", data)}
  onError={(error) => console.error("解析失败:", error)}
  uploadToS3={false}
  uploadMode="btn"
/>

// 拖拽模式
<ExcelUploader
  visible={visible}
  onCancel={() => setVisible(false)}
  downLoadTemplate={handleDownloadTemplate}
  columnConfig={columnConfig}
  onSuccess={(data) => console.log("解析成功:", data)}
  onError={(error) => console.error("解析失败:", error)}
  uploadToS3={false}
  uploadMode="dragger"
  draggerConfig={{
    title: "拖拽或点击上传Excel文件",
    hint: "支持 .xlsx 和 .xls 格式"
  }}
/>
```

### S3 上传模式

```tsx
<ExcelUploader
  visible={visible}
  onCancel={() => setVisible(false)}
  downLoadTemplate={handleDownloadTemplate}
  columnConfig={columnConfig}
  uploadToS3={true}
  uploadMode="dragger"
  draggerConfig={{
    title: "拖拽或点击上传到S3",
    hint: "文件将直接上传到云存储",
  }}
  s3Config={{
    getPreSignatureUrl: "/api/s3/presigned-url",
    LOPDN: "your-domain",
    bucketName: "your-bucket",
    maxFileSize: 50,
    unit: "MB",
    needMd5Validete: true,
    onS3Success: (fileKey) => console.log("上传成功:", fileKey),
    onS3Error: (error) => console.error("上传失败:", error),
  }}
/>
```

## API 参数

### ExcelUploaderProps

| 参数             | 类型                                        | 必填 | 默认值         | 说明                                  |
| ---------------- | ------------------------------------------- | ---- | -------------- | ------------------------------------- |
| visible          | boolean                                     | ✅   | -              | 控制弹窗显示/隐藏                     |
| onCancel         | () => void                                  | ✅   | -              | 关闭弹窗回调                          |
| downLoadTemplate | () => void                                  | ✅   | -              | 下载模板回调                          |
| columnConfig     | ColumnConfig[]                              | ✅   | -              | Excel 列配置                          |
| onSuccess        | (data: any[]) => void                       | ❌   | -              | 成功回调（本地解析模式）              |
| onError          | (error: Error) => void                      | ❌   | -              | 错误回调                              |
| uploadToS3       | boolean                                     | ❌   | false          | 是否使用 S3 上传模式                  |
| uploadMode       | "btn" \| "icon" \| "dragger"                | ❌   | "btn"          | 上传交互模式                          |
| title            | string                                      | ❌   | "批量上传"     | 弹窗标题                              |
| description      | ReactNode                                   | ❌   | -              | 说明文字                              |
| showStepGuidance | boolean                                     | ❌   | false          | 是否显示步骤引导                      |
| downloadIconText | string                                      | ❌   | "批量上传模板" | 下载按钮文字                          |
| s3Config         | S3Config                                    | ❌   | -              | S3 上传配置（uploadToS3=true 时必填） |
| draggerConfig    | { title?: string; hint?: string }           | ❌   | -              | 拖拽模式配置                          |
| customValidators | Record<string, ValidatorFunction>           | ❌   | -              | 自定义验证器                          |
| postProcessor    | (data: any[]) => Promise<PostProcessResult> | ❌   | -              | 后处理函数                            |

### S3Config

| 参数               | 类型                                  | 必填 | 默认值 | 说明                      |
| ------------------ | ------------------------------------- | ---- | ------ | ------------------------- |
| getPreSignatureUrl | string                                | ✅   | -      | 获取预签名 URL 的接口地址 |
| LOPDN              | string                                | ✅   | -      | 服务域标识                |
| bucketName         | string                                | ❌   | -      | S3 桶名称                 |
| maxFileSize        | number                                | ❌   | 50     | 最大文件大小              |
| unit               | 'MB' \| 'GB' \| 'KB'                  | ❌   | 'MB'   | 文件大小单位              |
| needMd5Validete    | boolean                               | ❌   | false  | 是否需要 MD5 校验         |
| onS3Success        | (fileKey: string) => void             | ❌   | -      | S3 上传成功回调           |
| onS3Error          | (error: Error, body?: Object) => void | ❌   | -      | S3 上传失败回调           |

### ColumnConfig

| 参数      | 类型                                     | 必填 | 默认值   | 说明                           |
| --------- | ---------------------------------------- | ---- | -------- | ------------------------------ |
| key       | string                                   | ✅   | -        | 字段键名                       |
| title     | string                                   | ✅   | -        | 列标题                         |
| required  | boolean                                  | ❌   | false    | 是否必填                       |
| type      | 'string' \| 'number' \| 'date' \| 'enum' | ❌   | 'string' | 数据类型                       |
| options   | string[]                                 | ❌   | -        | 枚举选项（type='enum' 时使用） |
| validator | string                                   | ❌   | -        | 验证器名称                     |
| transform | (value: any) => any                      | ❌   | -        | 数据转换函数                   |

## 使用场景

### 1. 本地解析模式

适用于需要在前端直接处理 Excel 数据的场景：

- 数据预览和验证
- 实时数据处理
- 小文件快速处理
- 离线数据处理

### 2. S3 上传模式

适用于需要服务端处理 Excel 文件的场景：

- 大文件处理
- 复杂业务逻辑处理
- 数据持久化存储
- 异步批量处理

## 注意事项

1. **组件依赖**：S3 上传模式依赖 `FileUpload` 组件，确保项目中已正确引入
2. **文件格式**：仅支持 `.xlsx` 和 `.xls` 格式的 Excel 文件
3. **S3 配置**：使用 S3 上传模式时，必须提供完整的 `s3Config` 配置
4. **LOPDN 头部**：S3 上传时会自动添加 `LOP-DN` 请求头
5. **文件大小**：建议根据实际需求设置合适的文件大小限制

## 完整示例

参考 `ExcelUploaderExample.tsx` 文件查看完整的使用示例。
