.upload-list {
  &.picture-container {
    display: flex;
    gap: 8px;
  }

  .upload-btn {
    display: flex;
    flex-flow: column;
    align-items: center;
    justify-content: center;
    border: 1px dashed rgb(217, 217, 217);
    border-radius: 4px;

    i {
      font-style: normal;
      font-size: 14px;
    }
  }

  .pic-item {
    position: relative;

    &:hover {
      .action-btn {
        display: flex;
      }
    }

    .action-btn {
      position: absolute;
      display: none;
      top: 0;
      left: 0;
      z-index: 1;
      background-color: rgba(0, 0, 0, 0.3);
      align-items: center;
      justify-content: center;

      svg {
        cursor: pointer;
      }
    }
  }
}

.file-preview {
  position: relative;
  margin: 10px 0;
  svg {
    position: absolute;
    right: 10px;
    top: 0;
    cursor: pointer;
  }
}