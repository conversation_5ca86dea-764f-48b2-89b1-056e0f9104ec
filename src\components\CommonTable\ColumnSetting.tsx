import React, { useState, useEffect, useMemo, useRef } from 'react';
import { Popover, Checkbox, Button, Tooltip, Tree, Typography } from 'antd';
import {
  SettingOutlined,
  VerticalAlignTopOutlined,
  VerticalAlignMiddleOutlined,
  VerticalAlignBottomOutlined
} from '@ant-design/icons';
import type { CheckboxChangeEvent } from 'antd/es/checkbox';
import type { ColumnsType } from 'antd/es/table';
import type { DataNode } from 'antd/es/tree';
import './index.scss';

export interface ColumnState {
  show: boolean;
  fixed?: 'left' | 'right' | undefined;
  order?: number;
  disable?: boolean;
}

export interface ColumnSettingProps {
  columns: ColumnsType<any>;
  columnsMap: Record<string, ColumnState>;
  onColumnsMapChange: (map: Record<string, ColumnState>) => void;
  tableKey?: string;
  defaultColumnsMap?: Record<string, ColumnState>;
  checkedReset?: boolean;
  checkable?: boolean;
  draggable?: boolean;
  showListItemOption?: boolean;
  listsHeight?: number;
  // 持久化类型
  persistenceType?: 'localStorage' | 'sessionStorage' | 'api';
  // API 相关配置
  api?: {
    // API 请求地址
    saveUrl?: string;
    // 额外的请求参数
    params?: Record<string, any>;
  };
  // 通过 API 重置列配置的回调函数
  onResetViaApi?: (tableKey: string, defaultColumnsMap: Record<string, ColumnState>) => Promise<void>;
}

// 生成列的唯一键
const genColumnKey = (key?: React.Key, index?: number) => {
  if (key) {
    return key.toString();
  }
  return `${index}`;
};

// 固定列的工具提示图标
const ToolTipIcon: React.FC<{
  title: string;
  columnKey: string;
  show: boolean;
  fixed: 'left' | 'right' | undefined;
  children?: React.ReactNode;
  columnsMap: Record<string, ColumnState>;
  onColumnsMapChange: (map: Record<string, ColumnState>) => void;
}> = ({ title, show, children, columnKey, fixed, columnsMap, onColumnsMapChange }) => {
  if (!show) {
    return null;
  }

  return (
    <Tooltip title={title}>
      <span
        onClick={(e) => {
          e.stopPropagation();
          e.preventDefault();
          // 直接使用传入的 columnsMap 和 onColumnsMapChange
          const config = columnsMap[columnKey] || {};
          const columnKeyMap = {
            ...columnsMap,
            [columnKey]: { ...config, fixed } as ColumnState,
          };
          onColumnsMapChange(columnKeyMap);
        }}
      >
        {children}
      </span>
    </Tooltip>
  );
};

// 列表项组件
const CheckboxListItem: React.FC<{
  columnKey: string;
  className?: string;
  title?: React.ReactNode;
  fixed?: boolean | 'left' | 'right';
  showListItemOption?: boolean;
  isLeaf?: boolean;
  columnsMap: Record<string, ColumnState>;
  onColumnsMapChange: (map: Record<string, ColumnState>) => void;
}> = ({ columnKey, isLeaf, title, className, fixed, showListItemOption, columnsMap, onColumnsMapChange }) => {
  return (
    <span className={`${className}-list-item`} key={columnKey}>
      <div className={`${className}-list-item-title`}>
        {title}
      </div>
      {showListItemOption && !isLeaf ? (
        <span className={`${className}-list-item-option`}>
          <ToolTipIcon
            columnKey={columnKey}
            fixed="left"
            title="固定在左侧"
            show={fixed !== 'left'}
            columnsMap={columnsMap}
            onColumnsMapChange={onColumnsMapChange}
          >
            <VerticalAlignTopOutlined />
          </ToolTipIcon>
          <ToolTipIcon
            columnKey={columnKey}
            fixed={undefined}
            title="不固定"
            show={!!fixed}
            columnsMap={columnsMap}
            onColumnsMapChange={onColumnsMapChange}
          >
            <VerticalAlignMiddleOutlined />
          </ToolTipIcon>
          <ToolTipIcon
            columnKey={columnKey}
            fixed="right"
            title="固定在右侧"
            show={fixed !== 'right'}
            columnsMap={columnsMap}
            onColumnsMapChange={onColumnsMapChange}
          >
            <VerticalAlignBottomOutlined />
          </ToolTipIcon>
        </span>
      ) : null}
    </span>
  );
};

// 列表组件
const CheckboxList: React.FC<{
  list: ColumnsType<any>;
  className?: string;
  title: string;
  draggable: boolean;
  checkable: boolean;
  showListItemOption: boolean;
  showTitle?: boolean;
  listHeight?: number;
  columnsMap: Record<string, ColumnState>;
  onColumnsMapChange: (map: Record<string, ColumnState>) => void;
  sortKeyColumns: string[];
  setSortKeyColumns: (keys: string[]) => void;
  groupFixedType?: 'left' | 'right' | undefined;
  onDragToGroup?: (key: string, targetGroup: 'left' | 'right' | undefined) => void;
}> = ({
  list,
  draggable,
  checkable,
  showListItemOption,
  className,
  showTitle = true,
  title: listTitle,
  listHeight = 280,
  columnsMap,
  onColumnsMapChange,
  sortKeyColumns,
  setSortKeyColumns,
  groupFixedType,
  onDragToGroup
}) => {
  const show = list && list.length > 0;

  // 构建树形数据
  const treeDataConfig = useMemo(() => {
    if (!show) return {};

    const checkedKeys: string[] = [];
    const treeMap = new Map<string | number, DataNode>();

    const loopData = (data: any[]): DataNode[] =>
      data.map(({ key, dataIndex, children, ...rest }, index) => {
        const columnKey = genColumnKey(key || dataIndex, index);
        const config = columnsMap[columnKey] || { show: true };

        if (config.show !== false && !children) {
          checkedKeys.push(columnKey);
        }

        const item: DataNode = {
          key: columnKey,
          ...rest,
          selectable: false,
          disabled: config.disable === true,
          disableCheckbox: config.disable === true,
        };

        if (children) {
          item.children = loopData(children);
          // 如果children 已经全部是show了，把自己也设置为show
          if (item.children?.every((childrenItem) => checkedKeys?.includes(childrenItem.key as string))) {
            checkedKeys.push(columnKey);
          }
        }

        treeMap.set(columnKey, item);
        return item;
      });

    return { list: loopData(list), keys: checkedKeys, map: treeMap };
  }, [columnsMap, list, show]);

  // 移动到指定的位置
  const move = (id: React.Key, targetId: React.Key, dropPosition: number) => {
    const newMap = { ...columnsMap };
    const newColumns = [...sortKeyColumns];

    const findIndex = newColumns.findIndex((columnKey) => columnKey === id);
    const targetIndex = newColumns.findIndex((columnKey) => columnKey === targetId);

    if (findIndex < 0) return;

    const isDownward = dropPosition >= findIndex;
    const targetItem = newColumns[findIndex];
    newColumns.splice(findIndex, 1);

    if (dropPosition === 0) {
      newColumns.unshift(targetItem);
    } else {
      newColumns.splice(isDownward ? targetIndex : targetIndex + 1, 0, targetItem);
    }

    // 重新生成排序数组
    newColumns.forEach((key, order) => {
      newMap[key] = { ...(newMap[key] || {}), order };
    });

    // 更新数组
    onColumnsMapChange(newMap);
    setSortKeyColumns(newColumns);
  };

  // 选中反选功能
  const onCheckTree = (e: any) => {
    const newColumnMap = { ...columnsMap };

    const loopSetShow = (key: string | number) => {
      const newSetting = { ...newColumnMap[key] };
      newSetting.show = e.checked;

      // 如果含有子节点，也要选中
      if (treeDataConfig.map?.get(key)?.children) {
        treeDataConfig.map
          .get(key)
          ?.children?.forEach((item) => loopSetShow(item.key as string));
      }

      newColumnMap[key] = newSetting;
    };

    loopSetShow(e.node.key);
    onColumnsMapChange({ ...newColumnMap });
  };

  if (!show) {
    return null;
  }

  const listDom = (
    <Tree
      itemHeight={24}
      draggable={draggable && !!treeDataConfig.list?.length && treeDataConfig.list?.length > 1}
      checkable={checkable}
      onDrop={(info) => {
        const dropKey = info.node.key;
        const dragKey = info.dragNode.key;
        const { dropPosition, dropToGap } = info;
        const position = dropPosition === -1 || !dropToGap ? dropPosition + 1 : dropPosition;

        // 如果是拖拽到不同的分组，需要更新fixed属性
        const dragNodeFixed = columnsMap[dragKey as string]?.fixed;

        // 如果拖拽到了不同的分组，则更新fixed属性
        if (dragNodeFixed !== groupFixedType && onDragToGroup) {
          onDragToGroup(dragKey as string, groupFixedType);
        }

        // 处理列的排序
        move(dragKey, dropKey, position);
      }}
      blockNode
      onCheck={(_, e) => onCheckTree(e)}
      checkedKeys={treeDataConfig.keys}
      showLine={false}
      titleRender={(_node) => {
        const node = { ..._node, children: undefined };
        if (!node.title) return null;
        const normalizedTitle = typeof node.title === 'function' ? node.title(node) : node.title;
        const wrappedTitle = (
          <Typography.Text style={{ width: 180 }} ellipsis={true}>
            {normalizedTitle as React.ReactNode}
          </Typography.Text>
        );

        return (
          <CheckboxListItem
            className={className}
            {...node}
            showListItemOption={showListItemOption}
            title={wrappedTitle}
            columnKey={node.key as string}
            fixed={columnsMap[node.key as string]?.fixed}
            columnsMap={columnsMap}
            onColumnsMapChange={onColumnsMapChange}
          />
        );
      }}
      height={listHeight}
      treeData={treeDataConfig.list}
    />
  );

  return (
    <>
      {showTitle && <span className={`${className}-list-title`}>{listTitle}</span>}
      {listDom}
    </>
  );
};

// 分组复选框列表
const GroupCheckboxList: React.FC<{
  localColumns: ColumnsType<any>;
  className?: string;
  draggable: boolean;
  checkable: boolean;
  showListItemOption: boolean;
  listsHeight?: number;
  columnsMap: Record<string, ColumnState>;
  onColumnsMapChange: (map: Record<string, ColumnState>) => void;
  sortKeyColumns: string[];
  setSortKeyColumns: (keys: string[]) => void;
}> = ({
  localColumns,
  className,
  draggable,
  checkable,
  showListItemOption,
  listsHeight,
  columnsMap,
  onColumnsMapChange,
  sortKeyColumns,
  setSortKeyColumns
}) => {
  // 分组列表
  const rightList: ColumnsType<any> = [];
  const leftList: ColumnsType<any> = [];
  const list: ColumnsType<any> = [];

  // 根据列的fixed属性分组
  localColumns.forEach((item) => {
    // 使用columnsMap中的fixed属性而不是列定义中的fixed属性
    const columnKey = genColumnKey(item.key || (item as any).dataIndex, localColumns.indexOf(item));
    const fixed = columnsMap[columnKey]?.fixed || item.fixed;

    if (fixed === 'left') {
      leftList.push(item);
      return;
    }
    if (fixed === 'right') {
      rightList.push(item);
      return;
    }
    list.push(item);
  });

  const showRight = rightList && rightList.length > 0;
  const showLeft = leftList && leftList.length > 0;

  // 处理拖拽到不同分组的操作
  const handleDragToGroup = (key: string, targetGroup: 'left' | 'right' | undefined) => {
    const newColumnsMap = { ...columnsMap };
    const config = newColumnsMap[key] || {};

    // 更新fixed属性
    newColumnsMap[key] = { ...config, fixed: targetGroup };

    // 更新列状态
    onColumnsMapChange(newColumnsMap);
  };

  return (
    <div
      className={`${className}-list ${showRight || showLeft ? `${className}-list-group` : ''}`}
    >
      {/* 左侧固定列组 */}
      <CheckboxList
        title="固定在左侧"
        list={leftList}
        draggable={draggable}
        checkable={checkable}
        showListItemOption={showListItemOption}
        className={className}
        listHeight={listsHeight}
        columnsMap={columnsMap}
        onColumnsMapChange={onColumnsMapChange}
        sortKeyColumns={sortKeyColumns}
        setSortKeyColumns={setSortKeyColumns}
        groupFixedType="left"
        onDragToGroup={handleDragToGroup}
      />
      {/* 不固定列组 */}
      <CheckboxList
        list={list}
        draggable={draggable}
        checkable={checkable}
        showListItemOption={showListItemOption}
        title="不固定"
        showTitle={showLeft || showRight}
        className={className}
        listHeight={listsHeight}
        columnsMap={columnsMap}
        onColumnsMapChange={onColumnsMapChange}
        sortKeyColumns={sortKeyColumns}
        setSortKeyColumns={setSortKeyColumns}
        groupFixedType={undefined}
        onDragToGroup={handleDragToGroup}
      />
      {/* 右侧固定列组 */}
      <CheckboxList
        title="固定在右侧"
        list={rightList}
        draggable={draggable}
        checkable={checkable}
        showListItemOption={showListItemOption}
        className={className}
        listHeight={listsHeight}
        columnsMap={columnsMap}
        onColumnsMapChange={onColumnsMapChange}
        sortKeyColumns={sortKeyColumns}
        setSortKeyColumns={setSortKeyColumns}
        groupFixedType="right"
        onDragToGroup={handleDragToGroup}
      />
    </div>
  );
};

const ColumnSetting: React.FC<ColumnSettingProps> = ({
  columns,
  columnsMap,
  onColumnsMapChange,
  tableKey,
  defaultColumnsMap,
  checkedReset = true,
  checkable = true,
  draggable = true,
  showListItemOption = true,
  listsHeight,
  persistenceType,
  onResetViaApi,
}) => {
  const columnRef = useRef<Record<string, ColumnState> | null>(null);

  // 初始化排序键列表
  const [sortKeyColumns, setSortKeyColumns] = useState<string[]>(() => {
    const sortKeys = Object.keys(columnsMap).sort((a, b) => {
      const orderA = columnsMap[a]?.order || 0;
      const orderB = columnsMap[b]?.order || 0;
      return orderA - orderB;
    });
    return sortKeys;
  });

  useEffect(() => {
    // 保存初始状态用于重置
    if (!columnRef.current) {
      columnRef.current = JSON.parse(JSON.stringify(defaultColumnsMap || columnsMap));
    }
  }, []);

  // 设置全部选中，或全部未选中
  const setAllSelectAction = (show: boolean = true) => {
    const columnKeyMap = {} as Record<string, ColumnState>;

    const loopColumns = (columnsData: any[]) => {
      columnsData.forEach(({ key, fixed, dataIndex, children, disable }, index) => {
        const columnKey = genColumnKey(key || dataIndex, index);
        if (columnKey) {
          // 保留当前列的fixed属性，如果不存在则使用列定义中的fixed属性
          const currentConfig = columnsMap[columnKey] || {};
          columnKeyMap[columnKey] = {
            show: disable ? currentConfig.show : show,
            fixed: currentConfig.fixed !== undefined ? currentConfig.fixed : fixed,
            disable,
            order: currentConfig.order,
          };
        }
        if (children) {
          loopColumns(children);
        }
      });
    };

    loopColumns(columns);
    onColumnsMapChange(columnKeyMap);
  };

  // 全选和反选
  const checkedAll = (e: CheckboxChangeEvent) => {
    if (e.target.checked) {
      setAllSelectAction();
    } else {
      setAllSelectAction(false);
    }
  };

  // 重置项目
  const resetToDefault = async () => {
    if (columnRef.current) {
      onColumnsMapChange(columnRef.current);

      // 如果提供了tableKey，则根据持久化类型保存配置
      if (tableKey) {
        if (persistenceType === 'api' && onResetViaApi) {
          // 通过 API 重置
          await onResetViaApi(tableKey, columnRef.current);
        } else if (persistenceType === 'localStorage') {
          // 保存到 localStorage
          localStorage.setItem(`table-columns-${tableKey}`, JSON.stringify(columnRef.current));
        } else if (persistenceType === 'sessionStorage') {
          // 保存到 sessionStorage
          sessionStorage.setItem(`table-columns-${tableKey}`, JSON.stringify(columnRef.current));
        }
      }
    } else {
      const newColumnsMap = defaultColumnsMap ||
        columns.reduce((prev, current, index) => {
          const key = genColumnKey(current.key || (current as any).dataIndex, index);
          if (key) {
            prev[key] = { show: true };
          }
          return prev;
        }, {} as Record<string, ColumnState>);

      onColumnsMapChange(newColumnsMap);

      // 如果提供了tableKey，则根据持久化类型保存配置
      if (tableKey) {
        if (persistenceType === 'api' && onResetViaApi) {
          // 通过 API 重置
          await onResetViaApi(tableKey, newColumnsMap);
        } else if (persistenceType === 'localStorage') {
          // 保存到 localStorage
          localStorage.setItem(`table-columns-${tableKey}`, JSON.stringify(newColumnsMap));
        } else if (persistenceType === 'sessionStorage') {
          // 保存到 sessionStorage
          sessionStorage.setItem(`table-columns-${tableKey}`, JSON.stringify(newColumnsMap));
        }
      }
    }
  };

  // 未选中的 key 列表
  const unCheckedKeys = Object.values(columnsMap).filter(
    (value) => !value || value.show === false
  );

  // 是否已经选中
  const indeterminate =
    unCheckedKeys.length > 0 && unCheckedKeys.length !== columns.length;

  // 列设置菜单

  const menu = (
    <div className="column-setting-dropdown">
      <div className="column-setting-title">
        {checkable === false ? (
          <div />
        ) : (
          <Checkbox
            indeterminate={indeterminate}
            checked={unCheckedKeys.length === 0 && unCheckedKeys.length !== columns.length}
            onChange={(e) => checkedAll(e)}
          >
            列展示
          </Checkbox>
        )}
        {checkedReset ? (
          <a onClick={resetToDefault} className="column-setting-action-rest-button">
            重置
          </a>
        ) : null}
      </div>
      <div>
        <GroupCheckboxList
          checkable={checkable}
          draggable={draggable}
          showListItemOption={showListItemOption}
          className="column-setting"
          localColumns={columns}
          listsHeight={listsHeight}
          columnsMap={columnsMap}
          onColumnsMapChange={onColumnsMapChange}
          sortKeyColumns={sortKeyColumns}
          setSortKeyColumns={setSortKeyColumns}
        />
      </div>
    </div>
  );

  return (
    <Popover
      content={menu}
      trigger="click"
      className="column-setting-overlay"
      getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
    >
      <Tooltip title="列设置">
        <Button
          type="text"
          icon={<SettingOutlined />}
          className="column-setting-btn"
        />
      </Tooltip>
    </Popover>
  );
};

export default ColumnSetting;
